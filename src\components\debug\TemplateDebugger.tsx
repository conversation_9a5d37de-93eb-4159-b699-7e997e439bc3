import React, { useState, useEffect } from 'react';
import { useImportTemplateStore } from '../../store/importTemplateStore';
import { useTestTemplateStore } from '../../store/testTemplateStore';

export const TemplateDebugger: React.FC = () => {
  const { templates, createTemplate, getDefaultTemplate } = useImportTemplateStore();
  const { templates: testTemplates, addTemplate: addTestTemplate } = useTestTemplateStore();
  const [debugInfo, setDebugInfo] = useState<string>('');

  useEffect(() => {
    const info = [
      `📊 Main templates: ${templates.length}`,
      `📋 Main names: ${templates.map(t => t.name).join(', ')}`,
      `🧪 Test templates: ${testTemplates.length}`,
      `🧪 Test names: ${testTemplates.map(t => t.name).join(', ')}`,
      `🎯 Default template: ${getDefaultTemplate()?.name || 'None'}`,
      `💾 Main storage: ${localStorage.getItem('import-template-store') ? 'Found' : 'Not found'}`,
      `💾 Test storage: ${localStorage.getItem('test-template-store') ? 'Found' : 'Not found'}`
    ].join('\n');

    setDebugInfo(info);
  }, [templates, testTemplates]);

  const handleCreateTestTemplate = () => {
    const testTemplate = {
      name: `Test Template ${Date.now()}`,
      description: 'Template created for testing persistence',
      isDefault: false,
      columnMapping: {
        description: 'Descripción',
        type: 'Tipo',
        repetition_type: 'Tipo de Repetición',
        frequency_cycles: 'Ciclos de Frecuencia',
        frequency_repetitions: 'Repeticiones por Ciclo',
        time_records: 'Tiempo Observado (segundos)',
        activity_records: 'Actividad',
        supplements_percentage: 'Suplementos (%)',
        comments: 'Comentarios'
      },
      studyDefaults: {
        timeUnit: 'seconds',
        shiftMinutes: 455,
        contingency: 2,
        pointsPerHour: 136,
        normalActivity: 100,
        optimalActivity: 133
      }
    };

    const id = createTemplate(testTemplate);
    console.log('✅ Test template created with ID:', id);
  };

  const handleCheckStorage = () => {
    const storageData = localStorage.getItem('import-template-store');
    console.log('💾 Storage data:', storageData);
    
    if (storageData) {
      try {
        const parsed = JSON.parse(storageData);
        console.log('📊 Parsed storage:', parsed);
      } catch (error) {
        console.error('❌ Error parsing storage:', error);
      }
    }
  };

  const handleClearStorage = () => {
    localStorage.removeItem('import-template-store');
    console.log('🗑️ Storage cleared');
    window.location.reload();
  };

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50">
      <h3 className="font-bold text-sm mb-2">🔧 Template Debugger</h3>
      
      <pre className="text-xs bg-gray-100 p-2 rounded mb-3 whitespace-pre-wrap">
        {debugInfo}
      </pre>
      
      <div className="space-y-2">
        <button
          type="button"
          onClick={handleCreateTestTemplate}
          className="w-full bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600"
        >
          Create Main Template
        </button>

        <button
          type="button"
          onClick={() => addTestTemplate(`Test ${Date.now()}`)}
          className="w-full bg-purple-500 text-white px-3 py-1 rounded text-xs hover:bg-purple-600"
        >
          Create Test Template
        </button>

        <button
          type="button"
          onClick={handleCheckStorage}
          className="w-full bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600"
        >
          Check Storage
        </button>

        <button
          type="button"
          onClick={handleClearStorage}
          className="w-full bg-red-500 text-white px-3 py-1 rounded text-xs hover:bg-red-600"
        >
          Clear Storage
        </button>
      </div>
    </div>
  );
};
