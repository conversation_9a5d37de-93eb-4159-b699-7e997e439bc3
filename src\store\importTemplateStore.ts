import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface ImportTemplate {
  id: string;
  name: string;
  description: string;
  isDefault: boolean;
  columnMapping: {
    description: string;
    type: string;
    repetition_type: string;
    frequency_cycles: string;
    frequency_repetitions: string;
    time_records: string; // Para múltiples registros de tiempo
    activity_records: string; // Para múltiples actividades
    supplements_percentage: string;
    comments: string;
  };
  studyDefaults: {
    timeUnit: string;
    shiftMinutes: number;
    contingency: number;
    pointsPerHour: number;
    normalActivity: number;
    optimalActivity: number;
  };
  created_at: string;
  updated_at: string;
}

interface ImportTemplateState {
  templates: ImportTemplate[];
  selectedTemplateId: string | null;
  
  // Acciones CRUD
  createTemplate: (template: Omit<ImportTemplate, 'id' | 'created_at' | 'updated_at'>) => string;
  updateTemplate: (id: string, updates: Partial<ImportTemplate>) => void;
  deleteTemplate: (id: string) => void;
  duplicateTemplate: (id: string, newName: string) => string;
  
  // Gestión de plantilla por defecto
  setDefaultTemplate: (id: string) => void;
  getDefaultTemplate: () => ImportTemplate | null;
  
  // Selección de plantilla
  selectTemplate: (id: string | null) => void;
  getSelectedTemplate: () => ImportTemplate | null;
  
  // Utilidades
  getTemplateById: (id: string) => ImportTemplate | null;
  resetToDefaults: () => void;
}

const defaultTemplates: ImportTemplate[] = [
  {
    id: 'default-standard',
    name: 'Plantilla Estándar',
    description: 'Plantilla por defecto para importación de estudios estándar',
    isDefault: true,
    columnMapping: {
      description: 'Descripción',
      type: 'Tipo',
      repetition_type: 'Tipo de Repetición',
      frequency_cycles: 'Ciclos de Frecuencia',
      frequency_repetitions: 'Repeticiones por Ciclo',
      time_records: 'Tiempo Observado (segundos)',
      activity_records: 'Actividad',
      supplements_percentage: 'Suplementos (%)',
      comments: 'Comentarios'
    },
    studyDefaults: {
      timeUnit: 'seconds',
      shiftMinutes: 455,
      contingency: 2,
      pointsPerHour: 136,
      normalActivity: 100,
      optimalActivity: 133
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'default-simple',
    name: 'Plantilla Simplificada',
    description: 'Plantilla básica con campos mínimos requeridos',
    isDefault: false,
    columnMapping: {
      description: 'Descripción',
      type: 'Tipo',
      repetition_type: '',
      frequency_cycles: '',
      frequency_repetitions: '',
      time_records: 'Tiempo (seg)',
      activity_records: 'Actividad',
      supplements_percentage: 'Suplementos',
      comments: ''
    },
    studyDefaults: {
      timeUnit: 'seconds',
      shiftMinutes: 480,
      contingency: 0,
      pointsPerHour: 100,
      normalActivity: 100,
      optimalActivity: 133
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

export const useImportTemplateStore = create<ImportTemplateState>()(
  persist(
    (set, get) => ({
      templates: defaultTemplates,
      selectedTemplateId: 'default-standard',

      createTemplate: (templateData) => {
        console.log('🆕 Creating new template:', templateData.name);
        const id = crypto.randomUUID();
        const newTemplate: ImportTemplate = {
          ...templateData,
          id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        set(state => {
          const newState = {
            templates: [...state.templates, newTemplate]
          };
          console.log(`📊 Template created. Total templates: ${newState.templates.length}`);
          return newState;
        });

        return id;
      },

      updateTemplate: (id, updates) => {
        console.log('✏️ Updating template:', id);
        set(state => {
          const newState = {
            templates: state.templates.map(template =>
              template.id === id
                ? { ...template, ...updates, updated_at: new Date().toISOString() }
                : template
            )
          };
          console.log(`📊 Template updated. Total templates: ${newState.templates.length}`);
          return newState;
        });
      },

      deleteTemplate: (id) => {
        const state = get();
        const template = state.templates.find(t => t.id === id);
        
        if (!template) return;
        
        // No permitir eliminar si es la única plantilla
        if (state.templates.length <= 1) return;
        
        // Si se elimina la plantilla por defecto, asignar otra como defecto
        if (template.isDefault) {
          const otherTemplate = state.templates.find(t => t.id !== id);
          if (otherTemplate) {
            get().setDefaultTemplate(otherTemplate.id);
          }
        }
        
        // Si se elimina la plantilla seleccionada, seleccionar la por defecto
        if (state.selectedTemplateId === id) {
          const defaultTemplate = state.templates.find(t => t.isDefault && t.id !== id);
          set({ selectedTemplateId: defaultTemplate?.id || null });
        }

        set(state => ({
          templates: state.templates.filter(template => template.id !== id)
        }));
      },

      duplicateTemplate: (id, newName) => {
        const template = get().getTemplateById(id);
        if (!template) return '';

        const newId = get().createTemplate({
          ...template,
          name: newName,
          isDefault: false
        });

        return newId;
      },

      setDefaultTemplate: (id) => {
        set(state => ({
          templates: state.templates.map(template => ({
            ...template,
            isDefault: template.id === id,
            updated_at: template.id === id ? new Date().toISOString() : template.updated_at
          }))
        }));
      },

      getDefaultTemplate: () => {
        return get().templates.find(template => template.isDefault) || null;
      },

      selectTemplate: (id) => {
        set({ selectedTemplateId: id });
      },

      getSelectedTemplate: () => {
        const state = get();
        return state.templates.find(template => template.id === state.selectedTemplateId) || null;
      },

      getTemplateById: (id) => {
        return get().templates.find(template => template.id === id) || null;
      },

      resetToDefaults: () => {
        set({
          templates: defaultTemplates,
          selectedTemplateId: 'default-standard'
        });
      }
    }),
    {
      name: 'import-template-store',
      version: 1,
      storage: {
        getItem: (name) => {
          try {
            const value = localStorage.getItem(name);
            console.log(`📦 Loading templates from storage:`, value ? 'found' : 'not found');
            return value;
          } catch (error) {
            console.error('❌ Error loading templates from storage:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            localStorage.setItem(name, value);
            console.log(`💾 Templates saved to storage successfully`);
          } catch (error) {
            console.error('❌ Error saving templates to storage:', error);
          }
        },
        removeItem: (name) => {
          try {
            localStorage.removeItem(name);
            console.log(`🗑️ Templates removed from storage`);
          } catch (error) {
            console.error('❌ Error removing templates from storage:', error);
          }
        }
      },
      onRehydrateStorage: () => {
        console.log('🔄 Starting template store rehydration...');
        return (state, error) => {
          if (error) {
            console.error('❌ Template store rehydration failed:', error);
          } else {
            console.log('✅ Template store rehydrated successfully');
            console.log(`📊 Loaded ${state?.templates?.length || 0} templates`);
          }
        };
      }
    }
  )
);

// Make store available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).useImportTemplateStore = useImportTemplateStore;
}
