// Script para debuggear los filtros de la biblioteca
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ixqjqjqjqjqjqjqjqjqj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'; // Reemplazar con la clave real

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugLibraryFilters() {
    console.log('🔍 Debuggeando filtros de biblioteca...');
    
    try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            console.error('❌ No hay usuario autenticado');
            return;
        }
        
        console.log('👤 Usuario:', user.id);
        
        // Obtener estudios visibles en biblioteca
        const { data: studies, error } = await supabase
            .from('studies')
            .select('id, required_info, optional_info, elements')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
            
        if (error) {
            console.error('❌ Error:', error);
            return;
        }
        
        console.log(`📊 Total estudios del usuario: ${studies?.length || 0}`);
        
        // Filtrar estudios visibles en biblioteca
        const visibleStudies = studies?.filter(study => 
            study && typeof study === 'object' && 
            study.optional_info && 
            typeof study.optional_info === 'object' && 
            study.optional_info.visibleEnBiblioteca === true
        ) || [];
        
        console.log(`📚 Estudios visibles en biblioteca: ${visibleStudies.length}`);
        
        // Analizar empresas
        const companies = new Set();
        visibleStudies.forEach(study => {
            const company = study.required_info?.company;
            if (company) {
                companies.add(company);
                console.log(`🏢 Estudio "${study.required_info?.name}" - Empresa: "${company}"`);
            } else {
                console.log(`⚠️ Estudio "${study.required_info?.name}" - Sin empresa`);
            }
        });
        
        console.log(`🏢 Empresas únicas encontradas: ${companies.size}`);
        console.log('📋 Lista de empresas:', Array.from(companies));
        
        // Analizar elementos
        let totalElements = 0;
        visibleStudies.forEach(study => {
            const elements = study.elements || [];
            totalElements += elements.length;
            console.log(`📋 Estudio "${study.required_info?.name}" - ${elements.length} elementos`);
        });
        
        console.log(`📊 Total elementos: ${totalElements}`);
        
    } catch (error) {
        console.error('❌ Error general:', error);
    }
}

// Ejecutar cuando se cargue la página
if (typeof window !== 'undefined') {
    window.debugLibraryFilters = debugLibraryFilters;
    console.log('🔧 Función debugLibraryFilters disponible en window.debugLibraryFilters()');
}

export { debugLibraryFilters };
