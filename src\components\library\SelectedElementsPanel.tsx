import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Clock, Activity, Building2, Trash2, ShoppingCart } from 'lucide-react';
import { ElementInstance } from '../../types';
import { CreateStudyModal } from './CreateStudyModal';

interface SelectedElementsPanelProps {
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  onRemoveElement: (elementId: string, studyId: string) => void;
  onClearAll: () => void;
  onCreateStudy: (studyData: {
    name: string;
    company: string;
    date: string;
    normalActivity: number;
    optimalActivity: number;
  }) => Promise<void>;
  isVisible: boolean;
  onToggleVisibility: () => void;
  isCreatingStudy?: boolean;
  isDesktopMode?: boolean;
  isMobileMode?: boolean;
}

export const SelectedElementsPanel: React.FC<SelectedElementsPanelProps> = React.memo(({
  selectedElements,
  onRemoveElement,
  onClearAll,
  onCreateStudy,
  isVisible,
  onToggleVisibility,
  isCreatingStudy = false,
  isDesktopMode = false,
  isMobileMode = false
}) => {
  const { t } = useTranslation(['library', 'common']);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Agrupar elementos por estudio (memoizado)
  const elementsByStudy = useMemo(() => {
    return selectedElements.reduce((acc, item) => {
      if (!acc[item.studyId]) {
        acc[item.studyId] = {
          studyName: item.studyName,
          elements: []
        };
      }
      acc[item.studyId].elements.push(item);
      return acc;
    }, {} as Record<string, { studyName: string; elements: typeof selectedElements }>);
  }, [selectedElements]);

  const formatTime = (time: number | undefined) => {
    if (time === undefined || time === null) return '--';
    return time.toFixed(2);
  };

  // Botón flotante cuando el panel no está visible
  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          type="button"
          onClick={onToggleVisibility}
          className="bg-purple-600 text-white p-3 rounded-full shadow-lg hover:bg-purple-700 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <ShoppingCart className="w-5 h-5" />
            {selectedElements.length > 0 && (
              <span className="bg-purple-800 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {selectedElements.length}
              </span>
            )}
          </div>
        </button>
      </div>
    );
  }

  // Modal overlay y contenedor
  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onToggleVisibility}
      />

      {/* Modal Panel */}
      <div className="fixed inset-y-0 right-0 w-full max-w-md bg-white shadow-xl z-50 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <ShoppingCart className="w-5 h-5 text-purple-600" />
            <h3 className="font-semibold text-gray-900 text-lg">
              {t('selectedElements', { ns: 'library' })}
            </h3>
            <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full">
              {selectedElements.length}
            </span>
          </div>
          <button
            type="button"
            onClick={onToggleVisibility}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            title={t('close', { ns: 'common' })}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>
        {/* Content */}
        <div className="flex-1 overflow-y-auto">
        {selectedElements.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <ShoppingCart className="w-16 h-16 text-gray-300 mb-4" />
            <p className="text-center">
              {t('noElementsSelected', { ns: 'library' })}
            </p>
            <p className="text-sm text-center mt-2">
              {t('selectElementsToCreateStudy', { ns: 'library' })}
            </p>
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {Object.entries(elementsByStudy).map(([studyId, { studyName, elements }]) => (
              <div key={studyId} className="bg-gray-50 rounded-lg p-3">
                {/* Nombre del estudio origen */}
                <div className="flex items-center space-x-2 mb-3">
                  <Building2 className="w-4 h-4 text-gray-500" />
                  <h4 className="text-sm font-medium text-gray-700 truncate">
                    {studyName}
                  </h4>
                  <span className="text-xs text-gray-500">
                    ({elements.length} {t('elements', { ns: 'library' })})
                  </span>
                </div>

                {/* Lista de elementos del estudio */}
                <div className="space-y-2">
                  {elements.map(({ element }) => (
                    <div
                      key={element.id}
                      className="bg-white rounded-md p-2 flex items-center justify-between"
                    >
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {element.description || t('noDescription', { ns: 'common' })}
                        </p>
                        <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                          {element.time !== undefined && (
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{formatTime(element.time)}s</span>
                            </div>
                          )}
                          {element.activity !== undefined && (
                            <div className="flex items-center space-x-1">
                              <Activity className="w-3 h-3" />
                              <span>{element.activity}%</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => onRemoveElement(element.id, studyId)}
                        className="ml-2 p-1 text-red-500 hover:bg-red-50 rounded transition-colors"
                        title={t('remove', { ns: 'common' })}
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
        </div>

        {/* Footer con acciones */}
        {selectedElements.length > 0 && (
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              type="button"
              onClick={() => setShowCreateModal(true)}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isCreatingStudy}
            >
              {isCreatingStudy ? t('creating', { ns: 'common' }) : t('createStudyFromSelection', { ns: 'library' })}
            </button>
            <button
              type="button"
              onClick={onClearAll}
              className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium flex items-center justify-center space-x-2"
            >
              <Trash2 className="w-4 h-4" />
              <span>{t('clearAll', { ns: 'common' })}</span>
            </button>
          </div>
        )}

        {/* Modal de creación de estudio */}
        <CreateStudyModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          selectedElements={selectedElements}
          onCreateStudy={onCreateStudy}
          isCreating={isCreatingStudy}
        />
      </div>
    </>
  );
});
