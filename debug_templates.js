// Script para debuggear las plantillas
console.log('🔍 Debuggeando plantillas...');

// Verificar localStorage
function checkLocalStorage() {
    console.log('📦 Verificando localStorage...');
    
    // Buscar todas las claves relacionadas con plantillas
    const keys = Object.keys(localStorage);
    const templateKeys = keys.filter(key => 
        key.includes('template') || 
        key.includes('import') || 
        key.includes('zustand')
    );
    
    console.log('🔑 Claves relacionadas con plantillas:', templateKeys);
    
    templateKeys.forEach(key => {
        try {
            const value = localStorage.getItem(key);
            console.log(`📋 ${key}:`, JSON.parse(value || '{}'));
        } catch (error) {
            console.log(`📋 ${key}:`, localStorage.getItem(key));
        }
    });
    
    // Verificar específicamente la clave del store
    const storeKey = 'import-template-store';
    const storeData = localStorage.getItem(storeKey);
    console.log(`🏪 Store data (${storeKey}):`, storeData);
    
    if (storeData) {
        try {
            const parsed = JSON.parse(storeData);
            console.log('📊 Plantillas en store:', parsed.state?.templates?.length || 0);
            console.log('📋 Lista de plantillas:', parsed.state?.templates?.map(t => t.name) || []);
        } catch (error) {
            console.error('❌ Error parsing store data:', error);
        }
    }
}

// Verificar el estado actual del store
function checkStoreState() {
    console.log('🏪 Verificando estado del store...');
    
    // Intentar acceder al store si está disponible
    if (window.useImportTemplateStore) {
        const state = window.useImportTemplateStore.getState();
        console.log('📊 Templates en estado actual:', state.templates?.length || 0);
        console.log('📋 Lista actual:', state.templates?.map(t => t.name) || []);
        console.log('🎯 Template seleccionado:', state.selectedTemplateId);
    } else {
        console.log('⚠️ Store no disponible en window');
    }
}

// Función para limpiar localStorage de plantillas
function clearTemplateStorage() {
    console.log('🧹 Limpiando storage de plantillas...');
    localStorage.removeItem('import-template-store');
    console.log('✅ Storage limpiado');
}

// Función para forzar guardado
function forceSave() {
    console.log('💾 Forzando guardado...');
    
    if (window.useImportTemplateStore) {
        const store = window.useImportTemplateStore;
        const state = store.getState();
        
        // Crear una plantilla de prueba
        const testTemplate = {
            name: 'Plantilla de Prueba - ' + new Date().toLocaleTimeString(),
            description: 'Plantilla creada para prueba de persistencia',
            isDefault: false,
            columnMapping: {
                description: 'Descripción',
                type: 'Tipo',
                repetition_type: 'Tipo de Repetición',
                frequency_cycles: 'Ciclos de Frecuencia',
                frequency_repetitions: 'Repeticiones por Ciclo',
                time_records: 'Tiempo Observado (segundos)',
                activity_records: 'Actividad',
                supplements_percentage: 'Suplementos (%)',
                comments: 'Comentarios'
            },
            studyDefaults: {
                timeUnit: 'seconds',
                shiftMinutes: 455,
                contingency: 2,
                pointsPerHour: 136,
                normalActivity: 100,
                optimalActivity: 133
            }
        };
        
        const newId = state.createTemplate(testTemplate);
        console.log('✅ Plantilla de prueba creada con ID:', newId);
        
        // Verificar que se guardó
        setTimeout(() => {
            checkLocalStorage();
            checkStoreState();
        }, 100);
    }
}

// Función para verificar permisos de localStorage
function checkStoragePermissions() {
    console.log('🔐 Verificando permisos de localStorage...');
    
    try {
        const testKey = 'test-storage-' + Date.now();
        const testValue = 'test-value';
        
        localStorage.setItem(testKey, testValue);
        const retrieved = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);
        
        if (retrieved === testValue) {
            console.log('✅ localStorage funciona correctamente');
        } else {
            console.log('❌ localStorage no funciona correctamente');
        }
    } catch (error) {
        console.error('❌ Error con localStorage:', error);
    }
}

// Ejecutar todas las verificaciones
function debugTemplates() {
    console.log('🚀 Iniciando debug de plantillas...');
    checkStoragePermissions();
    checkLocalStorage();
    checkStoreState();
}

// Hacer funciones disponibles globalmente
if (typeof window !== 'undefined') {
    window.debugTemplates = debugTemplates;
    window.checkLocalStorage = checkLocalStorage;
    window.checkStoreState = checkStoreState;
    window.clearTemplateStorage = clearTemplateStorage;
    window.forceSave = forceSave;
    
    console.log('🔧 Funciones de debug disponibles:');
    console.log('  - window.debugTemplates()');
    console.log('  - window.checkLocalStorage()');
    console.log('  - window.checkStoreState()');
    console.log('  - window.clearTemplateStorage()');
    console.log('  - window.forceSave()');
}

// Auto-ejecutar
debugTemplates();
