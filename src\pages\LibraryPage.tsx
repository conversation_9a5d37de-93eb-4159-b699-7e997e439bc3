import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Loader2, FileSpreadsheet } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useElementSearchStore } from '../store/elementSearchStore';
import { StudyLibraryList } from '../components/library/StudyLibraryList';
import { SelectedElementsPanel } from '../components/library/SelectedElementsPanel';
import { LibrarySearchBar } from '../components/library/LibrarySearchBar';
import { ExcelImporterModal } from '../components/library/ExcelImporterModal';
import { useLibraryData } from '../hooks/useLibraryData';
import { ElementInstance, Study } from '../types';
import { supabase } from '../lib/supabase';

export const LibraryPage: React.FC = () => {
  const { t } = useTranslation(['library', 'common']);
  const navigate = useNavigate();
  const {
    searchResults,
    isLoading,
    searchElements
  } = useElementSearchStore();

  const [showSelectedPanel, setShowSelectedPanel] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedElements, setSelectedElements] = useState<Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>>([]);
  const [filters, setFilters] = useState<{
    company?: string;
    dateFrom?: string;
    dateTo?: string;
    hasElements?: boolean;
    sortBy?: 'name' | 'date' | 'elements' | 'company';
    sortOrder?: 'asc' | 'desc';
  }>({});
  const [isCreatingStudy, setIsCreatingStudy] = useState(false);
  const [showExcelImporter, setShowExcelImporter] = useState(false);

  // Usar el hook personalizado para manejar los datos de la biblioteca
  const { studiesWithElements, availableCompanies, stats } = useLibraryData({
    searchResults,
    searchTerm,
    filters
  });

  useEffect(() => {
    searchElements('');
  }, [searchElements]);

  // Realizar búsqueda cuando cambie el término
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchElements(searchTerm);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, searchElements]);

  const handleBack = () => {
    navigate('/');
  };

  const handleElementToggle = (element: ElementInstance, studyName: string, studyId: string) => {
    setSelectedElements(prev => {
      const isSelected = prev.some(sel => sel.element.id === element.id && sel.studyId === studyId);

      if (isSelected) {
        return prev.filter(sel => !(sel.element.id === element.id && sel.studyId === studyId));
      } else {
        return [...prev, { element, studyName, studyId }];
      }
    });
  };

  const handleRemoveElement = (elementId: string, studyId: string) => {
    setSelectedElements(prev =>
      prev.filter(sel => !(sel.element.id === elementId && sel.studyId === studyId))
    );
  };

  const handleClearAll = () => {
    setSelectedElements([]);
  };

  const handleCreateStudy = async (studyData: {
    name: string;
    company: string;
    date: string;
    normalActivity: number;
    optimalActivity: number;
  }) => {
    setIsCreatingStudy(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Crear suplementos para cada elemento seleccionado
      const supplements: Record<string, any> = {
        elements: {}
      };

      // Mapear elementos para el nuevo estudio
      const elements = selectedElements.map((sel, index) => {
        const elementObj: any = {
          id: sel.element.id,
          name: sel.element.name || sel.element.description || `Elemento ${index + 1}`,
          description: sel.element.description || '',
          time: sel.element.time || 0,
          type: sel.element.type || 'machine-stopped',
          position: index + 1,
          repetition_type: sel.element.repetition_type || 'repetitive',
          frequency_cycles: sel.element.frequency_cycles || 1,
          frequency_repetitions: sel.element.frequency_repetitions || 1,
          activity: sel.element.activity || 100
        };

        // Añadir los suplementos al elemento
        if (sel.element.supplements && sel.element.supplements.length > 0) {
          const existingSupplement = sel.element.supplements[0];

          supplements[sel.element.id] = {
            points: existingSupplement.points || {},
            is_forced: existingSupplement.is_forced || false,
            percentage: existingSupplement.percentage || 0,
            factor_selections: existingSupplement.factor_selections || {}
          };

          elementObj.supplements = [{
            points: existingSupplement.points || {},
            is_forced: existingSupplement.is_forced || false,
            percentage: existingSupplement.percentage || 0,
            factor_selections: existingSupplement.factor_selections || {}
          }];
        } else {
          supplements[sel.element.id] = {
            points: {},
            is_forced: false,
            percentage: 0,
            factor_selections: {}
          };

          elementObj.supplements = [{
            points: {},
            is_forced: false,
            percentage: 0,
            factor_selections: {}
          }];
        }

        return elementObj;
      }).filter(el => el.id) as ElementInstance[];

      // Crear registros de tiempo
      const time_records: Record<string, any> = {};
      selectedElements.forEach(sel => {
        if (sel.element.id) {
          if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
            time_records[sel.element.id] = sel.element.timeRecords.map(record => ({
              id: crypto.randomUUID(),
              time: record.time,
              activity: record.activity,
              elementId: sel.element.id,
              timestamp: record.timestamp || new Date().toISOString(),
              comment: record.comment || sel.element.description || ''
            }));
          } else if (sel.element.time) {
            time_records[sel.element.id] = [{
              id: crypto.randomUUID(),
              time: sel.element.time,
              activity: sel.element.activity || 100,
              elementId: sel.element.id,
              timestamp: new Date().toISOString(),
              comment: sel.element.description || ''
            }];
          }
        }
      });

      // Crear el objeto de estudio completo
      const study: Study = {
        id: crypto.randomUUID(),
        user_id: user.id,
        required_info: {
          name: studyData.name,
          company: studyData.company,
          date: studyData.date,
          activity_scale: {
            normal: studyData.normalActivity,
            optimal: studyData.optimalActivity
          }
        },
        optional_info: {
          tools: '',
          machine: '',
          section: '',
          operator: '',
          reference: '',
          technician: '',
          study_number: '',
          isEstimated: true
        },
        elements,
        time_records,
        supplements,
        crono_seguido_records: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Crear el estudio en la base de datos
      const { error } = await supabase
        .from('studies')
        .insert([study]);

      if (error) throw error;

      // Limpiar selección y cerrar panel
      setSelectedElements([]);
      setShowSelectedPanel(false);

      // Navegar a la página de estudios
      navigate('/');
    } catch (error) {
      console.error('Error creating study:', error);
      throw error;
    } finally {
      setIsCreatingStudy(false);
    }
  };

  const handleExcelImport = async (studyData: {
    name: string;
    company: string;
    date: string;
    timeUnit: string;
    shiftMinutes: number;
    contingency: number;
    pointsPerHour: number;
    normalActivity: number;
    optimalActivity: number;
    elements: any[];
    timeRecords: Record<string, any[]>;
    supplements: Record<string, any>;
  }) => {
    setIsCreatingStudy(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Usar los datos procesados directamente del importador
      const elements = studyData.elements.map((element, index) => ({
        ...element,
        position: index // Asegurar posición correcta
      })) as ElementInstance[];

      // Usar los suplementos procesados del importador
      const supplements = studyData.supplements;

      // Usar los registros de tiempo procesados del importador
      const time_records = studyData.timeRecords;

      // Crear el estudio
      const study: Study = {
        id: crypto.randomUUID(),
        user_id: user.id,
        required_info: {
          name: studyData.name,
          company: studyData.company,
          date: studyData.date,
          activity_scale: {
            normal: studyData.normalActivity,
            optimal: studyData.optimalActivity
          }
        },
        optional_info: {
          tools: '',
          machine: '',
          section: '',
          operator: 'Excel Import',
          reference: '',
          technician: 'Sistema',
          study_number: '',
          isEstimated: true,
          visibleEnBiblioteca: true, // Marcar como visible en biblioteca
          // Configuraciones específicas del estudio importado
          reportSettings: {
            timeUnit: studyData.timeUnit,
            shiftMinutes: studyData.shiftMinutes,
            contingency: studyData.contingency,
            pointsPerHour: studyData.pointsPerHour
          }
        },
        elements,
        time_records,
        supplements,
        crono_seguido_records: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Crear el estudio en la base de datos
      const { error } = await supabase
        .from('studies')
        .insert([study]);

      if (error) throw error;

      // Cerrar modal y refrescar datos
      setShowExcelImporter(false);
      searchElements(''); // Refrescar la búsqueda para mostrar el nuevo estudio

    } catch (error) {
      console.error('Error importing Excel study:', error);
      throw error;
    } finally {
      setIsCreatingStudy(false);
    }
  };

  // Función para mapear tipos de elemento desde Excel
  const mapElementType = (excelType: string): string => {
    if (!excelType) return 'machine-stopped';

    const type = excelType.toLowerCase();

    if (type.includes('mp') || type.includes('parada') || type.includes('stopped')) {
      return 'machine-stopped';
    } else if (type.includes('mm') || type.includes('marcha') || type.includes('running')) {
      return 'machine-running';
    } else if (type.includes('tm') || type.includes('tiempo') || type.includes('time')) {
      return 'machine-time';
    }

    return 'machine-stopped';
  };

  // Función para mapear tipos de repetición desde Excel
  const mapRepetitionType = (excelType: string): string => {
    if (!excelType) return 'repetitive';

    const type = excelType.toLowerCase();

    if (type.includes('repetitiv') || type.includes('repetitive')) {
      return 'repetitive';
    } else if (type.includes('frecuen') || type.includes('frequency')) {
      return 'frequency';
    } else if (type.includes('maquin') || type.includes('machine')) {
      return 'machine';
    }

    return 'repetitive';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title={t('back', { ns: 'common' })}
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                {t('title', { ns: 'library' })}
              </h1>
            </div>

            <div className="flex items-center space-x-3">
              {/* Botón de importar Excel */}
              <button
                type="button"
                onClick={() => setShowExcelImporter(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center space-x-2"
                disabled={isCreatingStudy}
              >
                <FileSpreadsheet className="w-4 h-4" />
                <span>{t('importFromExcel', { ns: 'library' })}</span>
              </button>

              {selectedElements.length > 0 && (
                <>
                  <span className="text-sm text-gray-600">
                    {selectedElements.length} {t('selected', { ns: 'common' })}
                  </span>
                  <button
                    type="button"
                    onClick={() => setShowSelectedPanel(true)}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
                  >
                    {t('viewSelection', { ns: 'library' })}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Responsive Layout */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Desktop Layout: Two panels side-by-side */}
        <div className="hidden lg:flex lg:space-x-6">
          {/* Left Panel - Studies */}
          <div className="flex-1 space-y-6">
            {/* Search Bar */}
            <LibrarySearchBar
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filters={filters}
              onFiltersChange={setFilters}
              availableCompanies={availableCompanies}
            />

            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
                <span className="ml-2 text-gray-600">{t('loading', { ns: 'common' })}</span>
              </div>
            )}

            {/* Studies List */}
            {!isLoading && (
              <StudyLibraryList
                studies={studiesWithElements}
                selectedElements={selectedElements}
                onElementToggle={handleElementToggle}
                isLoading={isLoading}
              />
            )}
          </div>

          {/* Right Panel - Selected Elements (Desktop) */}
          <div className="w-96 flex-shrink-0">
            <div className="sticky top-8">
              <SelectedElementsPanel
                selectedElements={selectedElements}
                onRemoveElement={handleRemoveElement}
                onClearAll={handleClearAll}
                onCreateStudy={handleCreateStudy}
                isVisible={true}
                onToggleVisibility={() => {}}
                isCreatingStudy={isCreatingStudy}
                isDesktopMode={true}
              />
            </div>
          </div>
        </div>

        {/* Tablet Layout: Collapsible side panel */}
        <div className="hidden md:block lg:hidden">
          <div className="space-y-6">
            {/* Search Bar */}
            <LibrarySearchBar
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filters={filters}
              onFiltersChange={setFilters}
              availableCompanies={availableCompanies}
            />

            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
                <span className="ml-2 text-gray-600">{t('loading', { ns: 'common' })}</span>
              </div>
            )}

            {/* Studies List */}
            {!isLoading && (
              <StudyLibraryList
                studies={studiesWithElements}
                selectedElements={selectedElements}
                onElementToggle={handleElementToggle}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>

        {/* Mobile Layout: Tab navigation */}
        <div className="block md:hidden">
          <div className="space-y-6">
            {/* Mobile Tab Navigation */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                type="button"
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  !showSelectedPanel
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setShowSelectedPanel(false)}
              >
                {t('studies', { ns: 'library' })} ({studiesWithElements.length})
              </button>
              <button
                type="button"
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  showSelectedPanel
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setShowSelectedPanel(true)}
              >
                {t('selected', { ns: 'library' })} ({selectedElements.length})
              </button>
            </div>

            {/* Mobile Content */}
            {!showSelectedPanel ? (
              <>
                {/* Search Bar */}
                <LibrarySearchBar
                  searchTerm={searchTerm}
                  onSearchChange={setSearchTerm}
                  filters={filters}
                  onFiltersChange={setFilters}
                  availableCompanies={availableCompanies}
                />

                {/* Loading State */}
                {isLoading && (
                  <div className="flex items-center justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
                    <span className="ml-2 text-gray-600">{t('loading', { ns: 'common' })}</span>
                  </div>
                )}

                {/* Studies List */}
                {!isLoading && (
                  <StudyLibraryList
                    studies={studiesWithElements}
                    selectedElements={selectedElements}
                    onElementToggle={handleElementToggle}
                    isLoading={isLoading}
                  />
                )}
              </>
            ) : (
              <SelectedElementsPanel
                selectedElements={selectedElements}
                onRemoveElement={handleRemoveElement}
                onClearAll={handleClearAll}
                onCreateStudy={handleCreateStudy}
                isVisible={true}
                onToggleVisibility={() => {}}
                isCreatingStudy={isCreatingStudy}
                isMobileMode={true}
              />
            )}
          </div>
        </div>
      </main>

      {/* Selected Elements Panel - Only for tablet and mobile (not desktop) */}
      <div className="lg:hidden">
        <SelectedElementsPanel
          selectedElements={selectedElements}
          onRemoveElement={handleRemoveElement}
          onClearAll={handleClearAll}
          onCreateStudy={handleCreateStudy}
          isVisible={showSelectedPanel}
          onToggleVisibility={() => setShowSelectedPanel(!showSelectedPanel)}
          isCreatingStudy={isCreatingStudy}
          isMobileMode={true}
        />
      </div>

      {/* Excel Importer Modal */}
      <ExcelImporterModal
        isOpen={showExcelImporter}
        onClose={() => setShowExcelImporter(false)}
        onImport={handleExcelImport}
        isImporting={isCreatingStudy}
      />
    </div>
  );
};