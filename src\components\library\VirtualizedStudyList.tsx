import React, { useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Study, ElementInstance } from '../../types';
import { StudyLibraryCard } from './StudyLibraryCard';

interface VirtualizedStudyListProps {
  studies: Array<{
    study: Study;
    elements: ElementInstance[];
  }>;
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  onElementToggle: (element: ElementInstance, studyName: string, studyId: string) => void;
  isLoading?: boolean;
  height: number;
}

interface ItemData {
  studies: VirtualizedStudyListProps['studies'];
  selectedElements: VirtualizedStudyListProps['selectedElements'];
  onElementToggle: VirtualizedStudyListProps['onElementToggle'];
}

const StudyItem: React.FC<{
  index: number;
  style: React.CSSProperties;
  data: ItemData;
}> = ({ index, style, data }) => {
  const { studies, selectedElements, onElementToggle } = data;
  const studyData = studies[index];

  return (
    <div style={style}>
      <div className="px-3 pb-3">
        <StudyLibraryCard
          study={studyData.study}
          elements={studyData.elements}
          selectedElements={selectedElements}
          onElementToggle={onElementToggle}
        />
      </div>
    </div>
  );
};

export const VirtualizedStudyList: React.FC<VirtualizedStudyListProps> = ({
  studies,
  selectedElements,
  onElementToggle,
  height
}) => {
  const itemData = useMemo(() => ({
    studies,
    selectedElements,
    onElementToggle
  }), [studies, selectedElements, onElementToggle]);

  // Altura estimada por item (puede ajustarse según el contenido)
  const itemSize = 200;

  if (studies.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No hay estudios disponibles</p>
      </div>
    );
  }

  return (
    <List
      height={height}
      itemCount={studies.length}
      itemSize={itemSize}
      itemData={itemData}
      overscanCount={5}
    >
      {StudyItem}
    </List>
  );
};
