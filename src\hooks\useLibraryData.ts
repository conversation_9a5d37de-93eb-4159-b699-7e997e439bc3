import { useMemo } from 'react';
import { ElementInstance, Study } from '../types';

interface LibraryFilters {
  company?: string;
  dateFrom?: string;
  dateTo?: string;
  hasElements?: boolean;
  sortBy?: 'name' | 'date' | 'elements' | 'company';
  sortOrder?: 'asc' | 'desc';
}

interface StudyWithElements {
  study: Study;
  elements: ElementInstance[];
}

interface UseLibraryDataProps {
  searchResults: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  searchTerm: string;
  filters: LibraryFilters;
}

export const useLibraryData = ({ searchResults, searchTerm, filters }: UseLibraryDataProps) => {
  // Agrupar resultados por estudio
  const studiesWithElements = useMemo(() => {
    const studyMap = new Map<string, StudyWithElements>();
    
    searchResults.forEach(result => {
      const studyId = result.studyId;
      if (!studyMap.has(studyId)) {
        // Crear un objeto Study a partir de la información disponible
        const study: Study = {
          id: studyId,
          user_id: '', // Se llenará desde la base de datos
          required_info: {
            name: result.studyName,
            company: '', // Se puede extraer de los elementos si está disponible
            date: new Date().toISOString().split('T')[0],
            activity_scale: { normal: 100, optimal: 133 }
          },
          optional_info: {},
          elements: [],
          time_records: {},
          supplements: {},
          crono_seguido_records: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        studyMap.set(studyId, {
          study,
          elements: []
        });
      }
      studyMap.get(studyId)!.elements.push(result.element);
    });

    return Array.from(studyMap.values());
  }, [searchResults]);

  // Aplicar filtros de búsqueda y filtros avanzados
  const filteredStudies = useMemo(() => {
    let filtered = studiesWithElements;

    // Filtro por término de búsqueda
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(({ study, elements }) => {
        // Buscar en el nombre del estudio
        const studyNameMatch = study.required_info?.name?.toLowerCase().includes(term);
        
        // Buscar en los elementos
        const elementMatch = elements.some(element => 
          element.description?.toLowerCase().includes(term) ||
          element.name?.toLowerCase().includes(term)
        );
        
        return studyNameMatch || elementMatch;
      });
    }

    // Filtro por empresa
    if (filters.company) {
      filtered = filtered.filter(({ study }) => 
        study.required_info?.company === filters.company
      );
    }

    // Filtro por fecha desde
    if (filters.dateFrom) {
      filtered = filtered.filter(({ study }) => {
        const studyDate = study.required_info?.date;
        return studyDate && studyDate >= filters.dateFrom!;
      });
    }

    // Filtro por fecha hasta
    if (filters.dateTo) {
      filtered = filtered.filter(({ study }) => {
        const studyDate = study.required_info?.date;
        return studyDate && studyDate <= filters.dateTo!;
      });
    }

    // Filtro por estudios con elementos
    if (filters.hasElements) {
      filtered = filtered.filter(({ elements }) => 
        elements.some(el => !el.isPlaceholder)
      );
    }

    return filtered;
  }, [studiesWithElements, searchTerm, filters]);

  // Aplicar ordenamiento
  const sortedStudies = useMemo(() => {
    const sortBy = filters.sortBy || 'name';
    const sortOrder = filters.sortOrder || 'asc';

    return [...filteredStudies].sort((a, b) => {
      let valueA: string | number;
      let valueB: string | number;

      switch (sortBy) {
        case 'name':
          valueA = a.study.required_info?.name?.toLowerCase() || '';
          valueB = b.study.required_info?.name?.toLowerCase() || '';
          break;
        case 'date':
          valueA = new Date(a.study.required_info?.date || '').getTime();
          valueB = new Date(b.study.required_info?.date || '').getTime();
          break;
        case 'elements':
          valueA = a.elements.filter(el => !el.isPlaceholder).length;
          valueB = b.elements.filter(el => !el.isPlaceholder).length;
          break;
        case 'company':
          valueA = a.study.required_info?.company?.toLowerCase() || '';
          valueB = b.study.required_info?.company?.toLowerCase() || '';
          break;
        default:
          valueA = a.study.required_info?.name?.toLowerCase() || '';
          valueB = b.study.required_info?.name?.toLowerCase() || '';
      }

      let comparison = 0;
      if (valueA > valueB) {
        comparison = 1;
      } else if (valueA < valueB) {
        comparison = -1;
      }

      return sortOrder === 'asc' ? comparison : comparison * -1;
    });
  }, [filteredStudies, filters.sortBy, filters.sortOrder]);

  // Obtener empresas disponibles para filtros
  const availableCompanies = useMemo(() => {
    const companies = new Set<string>();
    studiesWithElements.forEach(({ study }) => {
      if (study.required_info?.company) {
        companies.add(study.required_info.company);
      }
    });
    return Array.from(companies).sort();
  }, [studiesWithElements]);

  // Estadísticas
  const stats = useMemo(() => {
    const totalStudies = studiesWithElements.length;
    const filteredStudiesCount = sortedStudies.length;
    const totalElements = studiesWithElements.reduce(
      (sum, { elements }) => sum + elements.filter(el => !el.isPlaceholder).length,
      0
    );
    const filteredElements = sortedStudies.reduce(
      (sum, { elements }) => sum + elements.filter(el => !el.isPlaceholder).length,
      0
    );

    return {
      totalStudies,
      filteredStudiesCount,
      totalElements,
      filteredElements
    };
  }, [studiesWithElements, sortedStudies]);

  return {
    studiesWithElements: sortedStudies,
    availableCompanies,
    stats
  };
};
