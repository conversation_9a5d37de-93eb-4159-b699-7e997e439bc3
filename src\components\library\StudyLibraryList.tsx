import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronDown, ChevronRight, Building2, Calendar, User, FileText } from 'lucide-react';
import { Study, ElementInstance } from '../../types';
import { StudyElementsCompactList } from './StudyElementsCompactList';

interface StudyLibraryListProps {
  studies: Array<{
    study: Study;
    elements: ElementInstance[];
  }>;
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  onElementToggle: (element: ElementInstance, studyName: string, studyId: string) => void;
  isLoading?: boolean;
}

export const StudyLibraryList: React.FC<StudyLibraryListProps> = React.memo(({
  studies,
  selectedElements,
  onElementToggle,
  isLoading = false
}) => {
  const { t } = useTranslation(['library', 'common']);
  const [expandedStudies, setExpandedStudies] = useState<Set<string>>(new Set());

  const toggleStudyExpansion = useCallback((studyId: string) => {
    setExpandedStudies(prev => {
      const newSet = new Set<string>();
      // Solo permitir un estudio expandido a la vez
      if (!prev.has(studyId)) {
        newSet.add(studyId);
      }
      return newSet;
    });
  }, []);

  const isStudyExpanded = useCallback((studyId: string) => expandedStudies.has(studyId), [expandedStudies]);

  const getSelectedElementsForStudy = useCallback((studyId: string) => {
    return selectedElements.filter(sel => sel.studyId === studyId);
  }, [selectedElements]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-gray-300 rounded"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (studies.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t('noStudiesInLibrary', { ns: 'library' })}
        </h3>
        <p className="text-gray-500">
          {t('noStudiesInLibraryDescription', { ns: 'library' })}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {studies.map(({ study, elements }) => {
        const isExpanded = isStudyExpanded(study.id);
        const selectedCount = getSelectedElementsForStudy(study.id).length;
        const totalElements = elements.filter(el => !el.isPlaceholder).length;

        return (
          <div
            key={study.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            {/* Header del estudio */}
            <div
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => toggleStudyExpansion(study.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {/* Icono de expansión */}
                  <div className="flex-shrink-0">
                    {isExpanded ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </div>

                  {/* Información del estudio */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {study.required_info?.name || t('untitledStudy', { ns: 'common' })}
                    </h3>
                    
                    <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                      {study.required_info?.company && (
                        <div className="flex items-center space-x-1">
                          <Building2 className="w-4 h-4" />
                          <span className="truncate">{study.required_info.company}</span>
                        </div>
                      )}
                      
                      {study.required_info?.date && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(study.required_info.date)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contador de elementos */}
                <div className="flex-shrink-0 ml-4">
                  <div className="flex items-center space-x-2">
                    {selectedCount > 0 && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {selectedCount} {t('selected', { ns: 'common' })}
                      </span>
                    )}
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {totalElements} {t('elements', { ns: 'library' })}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Lista de elementos (expandible) */}
            {isExpanded && (
              <div className="border-t border-gray-200 bg-gray-50">
                <StudyElementsCompactList
                  elements={elements.filter(el => !el.isPlaceholder)}
                  studyName={study.required_info?.name || t('untitledStudy', { ns: 'common' })}
                  studyId={study.id}
                  selectedElements={getSelectedElementsForStudy(study.id)}
                  onElementToggle={onElementToggle}
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
});
