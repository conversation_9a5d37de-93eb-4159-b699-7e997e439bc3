@import './styles/mobile.css';
@import './styles/navigation.css';
@import './styles/card-animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Estilos base */
@layer base {
  body {
    @apply bg-gray-100 text-gray-900;
  }
}

/* Componentes comunes */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-purple-600 text-white hover:bg-purple-700;
  }
  
  .input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500;
  }
}