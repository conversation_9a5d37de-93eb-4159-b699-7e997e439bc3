export default {
  title: 'Library',
  subtitle: 'Search and reuse elements from your studies',
  searchPlaceholder: 'Search elements by name or description',
  selectedElements: 'Selected elements: {{count}}',
  createNewStudy: 'Create new study',
  studyName: 'Study name',
  studyCompany: 'Company',
  studyDate: 'Date',
  studyDescription: 'Study description',
  normalActivity: 'Normal activity',
  optimalActivity: 'Optimal activity',
  fromStudy: 'From: {{name}}',
  noResults: 'No elements found',
  loading: 'Loading elements...',
  error: 'Error loading elements',
  studyCreated: 'Study created successfully',
  createError: 'Error creating study',
  companies: 'Companies',
  departments: 'Departments',
  operations: 'Operations',
  machines: 'Machines',
  parts: 'Parts',
  analysts: 'Analysts',
  new: 'New',
  edit: 'Edit',
  delete: 'Delete',
  name: 'Name',
  description: 'Description',
  code: 'Code',
  type: 'Type',
  saveSuccess: 'Item saved successfully',
  saveError: 'Error saving item',
  deleteSuccess: 'Item deleted successfully',
  deleteError: 'Error deleting item',
  confirmDelete: 'Are you sure you want to delete this item?',
  noItems: 'No items available',
  loadError: 'Error loading items',
  search: 'Search',
  filter: 'Filter',
  sort: 'Sort',
  fromLibrary: 'From Library',
  studies: 'Studies',
  libraryLoading: 'Loading...',
  libraryError: 'Error loading library',
  library: 'Library',
  libraryTab: 'Library',
  averageTab: 'Averages',
  calculateAverage: 'Calculate Average',
  averagedElement: 'Averaged Element',
  sendToLibrary: 'Send to Library',
  sendToAverage: 'Send to Average',
  baseTime: 'Base Time',
  totalTakes: 'Total Takes',
  clear: 'Clear',
  elements: 'elements',

  // New translations for redesigned library
  noStudiesInLibrary: 'No studies in library',
  noStudiesInLibraryDescription: 'Studies will appear here when they have elements marked as visible in the library',
  noElementsInStudy: 'This study has no available elements',
  searchStudiesAndElements: 'Search studies and elements...',
  viewSelection: 'View selection',
  selectedElements: 'Selected elements',
  noElementsSelected: 'No elements selected',
  selectElementsToCreateStudy: 'Select elements from different studies to create a new one',
  createStudyFromSelection: 'Create study from selection',
  allCompanies: 'All companies',
  dateFrom: 'Date from',
  dateTo: 'Date to',
  onlyStudiesWithElements: 'Only studies with elements',
  activeFilters: 'Active filters',
  applyFilters: 'Apply filters',
  withElements: 'With elements',
  errorCreatingStudy: 'Error creating study',
  fillRequiredFields: 'Please fill all required fields',
  selectAtLeastOneElement: 'Select at least one element',

  // Translations for creation modal
  studyNamePlaceholder: 'New study name...',
  companyPlaceholder: 'Company name...',
  companyRequired: 'Company is required',
  dateRequired: 'Date is required',
  activityRangeError: 'Activity must be between 1 and 200',
  optimalActivityError: 'Optimal activity must be greater than normal',
  creating: 'Creating...',

  // Translations for Excel importer
  importFromExcel: 'Import from Excel',
  uploadExcelFile: 'Upload Excel file',
  supportedFormats: 'Supported formats',
  invalidFileType: 'Invalid file type. Use .xlsx or .xls',
  fileNeedsHeaders: 'File must have at least one header row and data',
  errorReadingFile: 'Error reading Excel file',
  studyInformation: 'Study information',
  columnMapping: 'Column mapping',
  elementDescription: 'Element description',
  observedTime: 'Observed time',
  observedActivity: 'Observed activity',
  elementType: 'Element type',
  repetitionType: 'Repetition type',
  repetitionsPerCycle: 'Repetitions per cycle',
  frequencyCycles: 'Frequency cycles',
  selectColumn: 'Select column',
  dataPreview: 'Data preview',
  rows: 'rows',
  preview: 'Preview',
  readyToImport: 'Ready to import',
  elementsPreview: 'Elements preview',
  andMoreElements: 'and {{count}} more elements...',
  importing: 'Importing...',
  importStudy: 'Import study',
  errorsFound: 'Errors found',
  descriptionColumnRequired: 'Description column is required',
  studyNameRequired: 'Study name is required',
  errorImporting: 'Error importing file',
  downloadTemplate: 'Download template',

  // Translations for template management
  templateManager: 'Template Manager',
  manageTemplates: 'Manage Templates',
  createTemplate: 'Create Template',
  newTemplate: 'New Template',
  templateName: 'Template Name',
  studyDefaults: 'Study Defaults',
  timeUnit: 'Time Unit',
  shiftMinutes: 'Minutes per Shift',
  contingency: 'Contingency (%)',
  pointsPerHour: 'Points per Hour',
  normalActivity: 'Normal Activity',
  optimalActivity: 'Optimal Activity',
  setAsDefault: 'Set as Default',
  duplicate: 'Duplicate',
  confirmDeleteTemplate: 'Are you sure you want to delete this template?',

  // New mapping fields
  timeRecords: 'Time Records',
  activityRecords: 'Activity Records',
  supplementsPercentage: 'Supplements Percentage',
  frequencyRepetitions: 'Frequency Repetitions',

  // Placeholders for mapping fields
  descriptionPlaceholder: 'Column with element description',
  typePlaceholder: 'Column with element type',
  repetition_typePlaceholder: 'Column with repetition type',
  frequency_cyclesPlaceholder: 'Column with frequency cycles',
  frequency_repetitionsPlaceholder: 'Column with repetitions',
  time_recordsPlaceholder: 'Column with observed times',
  activity_recordsPlaceholder: 'Column with activities',
  supplements_percentagePlaceholder: 'Column with supplements percentage',
  commentsPlaceholder: 'Column with comments',

  // New translations
  selectImportTemplate: 'Select Import Template',
  saveAsTemplate: 'Save as Template',
  templateNamePlaceholder: 'New template name...'
};
