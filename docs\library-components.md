# Componentes de Biblioteca - Documentación

## Descripción General

Los componentes de biblioteca permiten a los usuarios navegar, buscar y seleccionar elementos de estudios marcados como biblioteca para crear nuevos estudios.

## Arquitectura

### Componentes Principales

#### `LibraryPage`
- **Propósito**: Página principal de la biblioteca
- **Características**:
  - Diseño responsivo (móvil, tablet, desktop)
  - Integración con Header estándar
  - Gestión de estado de selecciones
  - Importación desde Excel

#### `StudyLibraryList`
- **Propósito**: Lista de estudios de biblioteca
- **Características**:
  - Expansión/colapso de estudios (solo uno a la vez)
  - Memoización para optimización de rendimiento
  - Indicadores de elementos seleccionados

#### `StudyElementsCompactList`
- **Propósito**: Lista compacta de elementos dentro de un estudio
- **Características**:
  - Búsqueda interna de elementos (para estudios con >5 elementos)
  - Selección múltiple con checkboxes
  - Información resumida de elementos

#### `SelectedElementsPanel`
- **Propósito**: Panel de elementos seleccionados
- **Características**:
  - Modo desktop (panel lateral fijo)
  - Modo móvil (panel de navegación por pestañas)
  - Agrupación por estudio de origen
  - Creación de nuevo estudio

#### `LibrarySearchBar`
- **Propósito**: Barra de búsqueda y filtros
- **Características**:
  - Búsqueda por nombre de estudio y elementos
  - Filtros por empresa, fecha, elementos
  - Ordenamiento (nombre, fecha, elementos, empresa)
  - Panel de filtros expandible

### Componentes de Optimización

#### `VirtualizedStudyList`
- **Propósito**: Lista virtualizada para grandes cantidades de estudios
- **Uso**: Automático cuando hay >100 estudios
- **Beneficios**: Mejor rendimiento con listas largas

#### `StudyLibraryCard`
- **Propósito**: Tarjeta individual de estudio para virtualización
- **Características**: Memoización y callbacks optimizados

## Hooks Personalizados

### `useLibraryData`
- **Propósito**: Procesamiento y filtrado de datos de biblioteca
- **Características**:
  - Agrupación de elementos por estudio
  - Aplicación de filtros y búsqueda
  - Ordenamiento configurable
  - Estadísticas calculadas

## Flujo de Usuario

### Navegación Principal
1. **Entrada**: Usuario accede a `/library`
2. **Carga**: Se obtienen estudios marcados como biblioteca
3. **Vista**: Lista de estudios con información resumida
4. **Interacción**: Usuario puede expandir estudios para ver elementos

### Selección de Elementos
1. **Expansión**: Usuario hace clic en un estudio
2. **Vista**: Se muestran elementos numerados con checkboxes
3. **Selección**: Usuario marca elementos deseados
4. **Persistencia**: Selecciones se mantienen al cambiar entre estudios

### Creación de Estudio
1. **Panel**: Usuario ve elementos seleccionados en panel lateral/móvil
2. **Configuración**: Usuario configura nuevo estudio (nombre, empresa, etc.)
3. **Creación**: Sistema crea estudio con elementos seleccionados
4. **Navegación**: Usuario es dirigido al nuevo estudio

## Diseño Responsivo

### Desktop (≥1024px)
- Layout de dos paneles side-by-side
- Panel izquierdo: Lista de estudios y búsqueda
- Panel derecho: Elementos seleccionados (fijo)

### Tablet (768px - 1023px)
- Layout de una columna
- Panel de elementos seleccionados colapsable
- Misma funcionalidad que desktop

### Móvil (<768px)
- Navegación por pestañas
- Pestaña 1: Estudios y búsqueda
- Pestaña 2: Elementos seleccionados
- Botones de acción optimizados para táctil

## Optimizaciones de Rendimiento

### Memoización
- Componentes principales usan `React.memo`
- Callbacks optimizados con `useCallback`
- Cálculos pesados con `useMemo`

### Carga Lazy
- Elementos se cargan solo al expandir estudio
- Búsqueda interna solo para estudios con >5 elementos

### Virtualización
- Lista virtualizada para >100 estudios
- Reduce DOM y mejora scroll performance

## Integración con Sistema Existente

### Compatibilidad
- Mantiene estructura de datos existente
- Usa stores existentes (`elementSearchStore`)
- Compatible con sistema de créditos

### Migración
- Funcionalidad anterior se mantiene
- Nuevos componentes son opt-in
- Transición gradual posible

## Testing

### Componentes Testados
- `StudyLibraryList`
- `StudyElementsCompactList`
- `SelectedElementsPanel`
- `LibrarySearchBar`

### Casos de Prueba
- Renderizado sin errores
- Manejo de estados vacíos
- Interacciones de usuario
- Gestión de loading states

## Configuración

### Variables de Entorno
No requiere configuración adicional.

### Dependencias
- `react-window`: Para virtualización
- `react-i18next`: Para internacionalización
- Componentes existentes de la aplicación

## Mantenimiento

### Actualizaciones Futuras
- Monitorear rendimiento con métricas
- Ajustar tamaños de virtualización según uso
- Expandir filtros según necesidades de usuario

### Debugging
- Logs en desarrollo para filtros y búsquedas
- Error boundaries para componentes críticos
- Fallbacks para estados de error
