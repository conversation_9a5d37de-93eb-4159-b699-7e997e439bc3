import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { StudyLibraryList } from '../StudyLibraryList';
import { StudyElementsCompactList } from '../StudyElementsCompactList';
import { SelectedElementsPanel } from '../SelectedElementsPanel';
import { LibrarySearchBar } from '../LibrarySearchBar';

// Mock de react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: () => new Promise(() => {}),
    },
  }),
}));

// Mock data
const mockStudy = {
  id: 'test-study-1',
  user_id: 'user-1',
  required_info: {
    name: 'Test Study',
    company: 'Test Company',
    date: '2024-01-01',
    activity_scale: { normal: 100, optimal: 133 }
  },
  optional_info: {},
  elements: [],
  time_records: {},
  supplements: {},
  crono_seguido_records: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockElement = {
  id: 'element-1',
  name: 'Test Element',
  description: 'Test Description',
  type: 'machine-running',
  position: 0,
  repetition_type: 'repetitive',
  frequency_cycles: 1,
  frequency_repetitions: 1,
  time: 10.5,
  activity: 100
};

const mockStudiesWithElements = [
  {
    study: mockStudy,
    elements: [mockElement]
  }
];

const mockSelectedElements = [
  {
    element: mockElement,
    studyName: 'Test Study',
    studyId: 'test-study-1'
  }
];

describe('Library Components', () => {
  describe('StudyLibraryList', () => {
    it('renders without crashing', () => {
      render(
        <StudyLibraryList
          studies={mockStudiesWithElements}
          selectedElements={[]}
          onElementToggle={jest.fn()}
          isLoading={false}
        />
      );
      expect(screen.getByText('Test Study')).toBeInTheDocument();
    });

    it('shows loading state', () => {
      render(
        <StudyLibraryList
          studies={[]}
          selectedElements={[]}
          onElementToggle={jest.fn()}
          isLoading={true}
        />
      );
      // El componente no maneja loading internamente, pero no debería crashear
    });

    it('handles empty studies list', () => {
      render(
        <StudyLibraryList
          studies={[]}
          selectedElements={[]}
          onElementToggle={jest.fn()}
          isLoading={false}
        />
      );
      expect(screen.getByText('noStudiesInLibrary')).toBeInTheDocument();
    });
  });

  describe('StudyElementsCompactList', () => {
    it('renders elements correctly', () => {
      render(
        <StudyElementsCompactList
          elements={[mockElement]}
          studyName="Test Study"
          studyId="test-study-1"
          selectedElements={[]}
          onElementToggle={jest.fn()}
        />
      );
      expect(screen.getByText('Test Description')).toBeInTheDocument();
    });

    it('handles element selection', () => {
      const mockToggle = jest.fn();
      render(
        <StudyElementsCompactList
          elements={[mockElement]}
          studyName="Test Study"
          studyId="test-study-1"
          selectedElements={[]}
          onElementToggle={mockToggle}
        />
      );
      
      const elementCard = screen.getByText('Test Description').closest('div');
      if (elementCard) {
        fireEvent.click(elementCard);
        expect(mockToggle).toHaveBeenCalledWith(mockElement, 'Test Study', 'test-study-1');
      }
    });
  });

  describe('SelectedElementsPanel', () => {
    it('renders selected elements', () => {
      render(
        <SelectedElementsPanel
          selectedElements={mockSelectedElements}
          onRemoveElement={jest.fn()}
          onClearAll={jest.fn()}
          onCreateStudy={jest.fn()}
          isVisible={true}
          onToggleVisibility={jest.fn()}
          isCreatingStudy={false}
        />
      );
      expect(screen.getByText('selectedElements')).toBeInTheDocument();
    });

    it('shows empty state when no elements selected', () => {
      render(
        <SelectedElementsPanel
          selectedElements={[]}
          onRemoveElement={jest.fn()}
          onClearAll={jest.fn()}
          onCreateStudy={jest.fn()}
          isVisible={true}
          onToggleVisibility={jest.fn()}
          isCreatingStudy={false}
        />
      );
      expect(screen.getByText('noElementsSelected')).toBeInTheDocument();
    });
  });

  describe('LibrarySearchBar', () => {
    it('renders search input', () => {
      render(
        <LibrarySearchBar
          searchTerm=""
          onSearchChange={jest.fn()}
          filters={{}}
          onFiltersChange={jest.fn()}
          availableCompanies={[]}
        />
      );
      expect(screen.getByPlaceholderText('searchStudiesAndElements')).toBeInTheDocument();
    });

    it('handles search input changes', () => {
      const mockSearchChange = jest.fn();
      render(
        <LibrarySearchBar
          searchTerm=""
          onSearchChange={mockSearchChange}
          filters={{}}
          onFiltersChange={jest.fn()}
          availableCompanies={[]}
        />
      );
      
      const searchInput = screen.getByPlaceholderText('searchStudiesAndElements');
      fireEvent.change(searchInput, { target: { value: 'test search' } });
      expect(mockSearchChange).toHaveBeenCalledWith('test search');
    });
  });
});
