import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronDown, ChevronRight, Building2, Calendar, User, FileText } from 'lucide-react';
import { Study, ElementInstance } from '../../types';
import { StudyElementsCompactList } from './StudyElementsCompactList';

interface StudyLibraryCardProps {
  study: Study;
  elements: ElementInstance[];
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  onElementToggle: (element: ElementInstance, studyName: string, studyId: string) => void;
}

export const StudyLibraryCard: React.FC<StudyLibraryCardProps> = React.memo(({
  study,
  elements,
  selectedElements,
  onElementToggle
}) => {
  const { t } = useTranslation(['library', 'common']);
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpansion = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  const getSelectedElementsForStudy = useCallback(() => {
    return selectedElements.filter(sel => sel.studyId === study.id);
  }, [selectedElements, study.id]);

  const selectedCount = useMemo(() => {
    return getSelectedElementsForStudy().length;
  }, [getSelectedElementsForStudy]);

  const totalElements = useMemo(() => {
    return elements.filter(el => !el.isPlaceholder).length;
  }, [elements]);

  const formatDate = useCallback((dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header del estudio */}
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={toggleExpansion}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Icono de expansión */}
            <div className="flex-shrink-0">
              {isExpanded ? (
                <ChevronDown className="w-5 h-5 text-gray-400" />
              ) : (
                <ChevronRight className="w-5 h-5 text-gray-400" />
              )}
            </div>

            {/* Información del estudio */}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {study.required_info?.name || t('untitledStudy', { ns: 'common' })}
              </h3>
              
              <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                {study.required_info?.company && (
                  <div className="flex items-center space-x-1">
                    <Building2 className="w-4 h-4" />
                    <span className="truncate">{study.required_info.company}</span>
                  </div>
                )}
                
                {study.required_info?.date && (
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(study.required_info.date)}</span>
                  </div>
                )}
                
                <div className="flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span>{study.user_id}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Estadísticas */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-gray-900">{totalElements}</div>
              <div className="text-gray-500">{t('elements', { ns: 'library' })}</div>
            </div>
            
            {selectedCount > 0 && (
              <div className="text-center">
                <div className="font-semibold text-purple-600">{selectedCount}</div>
                <div className="text-purple-500">{t('selected', { ns: 'common' })}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Lista de elementos (expandible) */}
      {isExpanded && (
        <div className="border-t border-gray-200 bg-gray-50">
          <StudyElementsCompactList
            elements={elements.filter(el => !el.isPlaceholder)}
            studyName={study.required_info?.name || t('untitledStudy', { ns: 'common' })}
            studyId={study.id}
            selectedElements={getSelectedElementsForStudy()}
            onElementToggle={onElementToggle}
          />
        </div>
      )}
    </div>
  );
});
