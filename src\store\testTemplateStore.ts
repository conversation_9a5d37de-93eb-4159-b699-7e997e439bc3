import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface TestTemplate {
  id: string;
  name: string;
  created_at: string;
}

interface TestTemplateState {
  templates: TestTemplate[];
  addTemplate: (name: string) => void;
  clearTemplates: () => void;
}

export const useTestTemplateStore = create<TestTemplateState>()(
  persist(
    (set, get) => ({
      templates: [],
      
      addTemplate: (name: string) => {
        console.log('🧪 Adding test template:', name);
        const newTemplate: TestTemplate = {
          id: crypto.randomUUID(),
          name,
          created_at: new Date().toISOString()
        };
        
        set(state => {
          const newState = { templates: [...state.templates, newTemplate] };
          console.log('🧪 New state:', newState);
          return newState;
        });
      },
      
      clearTemplates: () => {
        console.log('🧪 Clearing test templates');
        set({ templates: [] });
      }
    }),
    {
      name: 'test-template-store',
      version: 1,
      onRehydrateStorage: () => {
        console.log('🧪 Test store rehydration starting...');
        return (state, error) => {
          if (error) {
            console.error('🧪 Test store rehydration failed:', error);
          } else {
            console.log('🧪 Test store rehydrated successfully');
            console.log('🧪 Loaded templates:', state?.templates?.length || 0);
          }
        };
      }
    }
  )
);

// Make available globally for testing
if (typeof window !== 'undefined') {
  (window as any).useTestTemplateStore = useTestTemplateStore;
}
